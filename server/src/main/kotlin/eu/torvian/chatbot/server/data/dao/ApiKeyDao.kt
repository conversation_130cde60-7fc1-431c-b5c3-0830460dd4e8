package eu.torvian.chatbot.server.data.dao

import arrow.core.Either
import eu.torvian.chatbot.common.models.Api<PERSON>ey
import eu.torvian.chatbot.server.data.dao.error.ApiKeyError

/**
 * Repository interface for managing API key configurations in the database.
 *
 * Defines operations for managing API key metadata. The actual credentials
 * are managed separately through the CredentialManager system.
 */
interface ApiKeyDao {
    /**
     * Retrieves all API key configurations.
     *
     * @return List of all API keys in the system.
     */
    suspend fun getAllApiKeys(): List<ApiKey>

    /**
     * Retrieves a single API key configuration by its unique identifier.
     *
     * @param id The unique identifier of the API key to retrieve.
     * @return Either an [ApiKeyError.ApiKeyNotFound] if not found, or the [ApiKey].
     */
    suspend fun getApiKeyById(id: String): Either<ApiKeyError.ApiKeyNotFound, ApiKey>

    /**
     * Inserts a new API key configuration into the database.
     *
     * @param id The unique identifier for the API key (must reference an existing secret alias).
     * @param name The display name for the API key.
     * @param description The description for the API key.
     * @return Either an [ApiKeyError.ForeignKeyViolation] if the secret alias doesn't exist,
     *         or the newly created [ApiKey].
     */
    suspend fun insertApiKey(
        id: String,
        name: String,
        description: String
    ): Either<ApiKeyError.ForeignKeyViolation, ApiKey>

    /**
     * Updates an existing API key configuration.
     *
     * @param apiKey The API key with updated values. The ID must match an existing key.
     * @return Either an [ApiKeyError.ApiKeyNotFound] if not found, or [Unit] on success.
     */
    suspend fun updateApiKey(apiKey: ApiKey): Either<ApiKeyError.ApiKeyNotFound, Unit>

    /**
     * Deletes an API key configuration from the database.
     * Note: This does not delete the associated secret - that should be handled separately.
     *
     * @param id The unique identifier of the API key to delete.
     * @return Either an [ApiKeyError.ApiKeyNotFound] if not found, or [Unit] on success.
     */
    suspend fun deleteApiKey(id: String): Either<ApiKeyError.ApiKeyNotFound, Unit>
}
