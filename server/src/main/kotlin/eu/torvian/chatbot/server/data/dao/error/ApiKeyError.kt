package eu.torvian.chatbot.server.data.dao.error

/**
 * Represents possible domain-specific errors that can occur during ApiKey data operations.
 */
sealed interface ApiKeyError {
    /**
     * Indicates that an API key with the specified ID was not found.
     */
    data class ApiKeyNotFound(val id: String) : ApiKeyError
    
    /**
     * Indicates that a foreign key constraint was violated during an insert or update.
     * This typically occurs when trying to reference a non-existent secret alias.
     */
    data class ForeignKeyViolation(val message: String) : ApiKeyError
}
