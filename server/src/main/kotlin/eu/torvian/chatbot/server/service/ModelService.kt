package eu.torvian.chatbot.server.service

import arrow.core.Either
import eu.torvian.chatbot.common.models.ApiKey
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.service.error.apikey.AddApiKeyError
import eu.torvian.chatbot.server.service.error.apikey.DeleteApiKeyError
import eu.torvian.chatbot.server.service.error.apikey.GetApiKeyError
import eu.torvian.chatbot.server.service.error.apikey.UpdateApiKeyError
import eu.torvian.chatbot.server.service.error.model.AddModelError
import eu.torvian.chatbot.server.service.error.model.DeleteModelError
import eu.torvian.chatbot.server.service.error.model.GetModelError
import eu.torvian.chatbot.server.service.error.model.UpdateModelError
import eu.torvian.chatbot.server.service.error.settings.AddSettingsError
import eu.torvian.chatbot.server.service.error.settings.DeleteSettingsError
import eu.torvian.chatbot.server.service.error.settings.GetSettingsByIdError
import eu.torvian.chatbot.server.service.error.settings.UpdateSettingsError

/**
 * Service interface for managing LLM Models and their Settings.
 */
interface ModelService {
    /**
     * Retrieves all LLM model configurations.
     */
    suspend fun getAllModels(): List<LLMModel>

    /**
     * Retrieves a single LLM model by its unique identifier.
     *
     * @param id The unique identifier of the LLM model to retrieve.
     * @return [Either] a [GetModelError.ModelNotFound] if the model doesn't exist, or the [LLMModel].
     */
    suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel>

    /**
     * Adds a new LLM model configuration.
     * Handles secure storage of the API key if provided.
     * @param name The display name for the model.
     * @param baseUrl The base URL for the LLM API.
     * @param type The type of LLM provider.
     * @param apiKeyId Optional reference ID to the securely stored API key.
     * @return Either an [AddModelError], or the newly created [LLMModel].
     */
    suspend fun addModel(name: String, baseUrl: String, type: String, apiKeyId: String?): Either<AddModelError, LLMModel>

    /**
     * Updates an existing LLM model configuration.
     * Handles updating the API key if a new one is provided.
     * @param model The LLMModel object containing the updated values. The ID must match an existing model.
     * @return Either an [UpdateModelError] or Unit if successful.
     */
    suspend fun updateModel(model: LLMModel): Either<UpdateModelError, Unit>

    /**
     * Deletes an LLM model configuration.
     * Handles deletion of associated settings. Does not delete the API key itself.
     * @param id The ID of the model to delete.
     * @return Either a [DeleteModelError], or Unit if successful.
     */
    suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit>

    /**
     * Retrieves a specific settings profile by ID.
     * @param id The ID of the settings profile.
     * @return Either a [GetSettingsByIdError] if not found, or the [ModelSettings].
     */
    suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings>

    /**
     * Retrieves all settings profiles stored in the database.
     * @return A list of all [ModelSettings] objects.
     */
    suspend fun getAllSettings(): List<ModelSettings>

    /**
     * Retrieves all settings profiles associated with a specific LLM model.
     * @param modelId The ID of the LLM model.
     * @return A list of [ModelSettings] for the model, or an empty list if none exist.
     */
    suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings>

    /**
     * Creates a new settings profile with the specified parameters.
     *
     * @param name The display name of the settings profile (e.g., "Default", "Creative")
     * @param modelId The ID of the LLM model this settings profile is associated with
     * @param systemMessage Optional system message/prompt to include in the conversation context
     * @param temperature Optional sampling temperature for text generation
     * @param maxTokens Optional maximum number of tokens to generate in the response
     * @param customParamsJson Optional model-specific parameters stored as a JSON string
     * @return [Either] an [AddSettingsError] if the associated model doesn't exist or insertion fails, or the newly created [ModelSettings]
     */
    suspend fun addSettings(
        name: String,
        modelId: Long,
        systemMessage: String?,
        temperature: Float?,
        maxTokens: Int?,
        customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings>

    /**
     * Updates an existing settings profile with new values.
     *
     * @param settings The ModelSettings object containing the updated values. The ID must match an existing settings profile.
     * @return [Either] an [UpdateSettingsError] if not found or update fails, or [Unit] on success
     */
    suspend fun updateSettings(settings: ModelSettings): Either<UpdateSettingsError, Unit>

    /**
     * Deletes a settings profile with the specified ID.
     *
     * @param id The unique identifier of the settings profile to delete
     * @return [Either] a [DeleteSettingsError] if not found, or [Unit] on success
     */
    suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit>

    /**
     * Checks if an API key is configured for a specific model.
     * @param modelId The ID of the model.
     * @return True if an API key ID is stored for the model, false otherwise.
     */
    suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean

    // --- API Key Management ---

    /**
     * Retrieves all API key configurations.
     */
    suspend fun getAllApiKeys(): List<ApiKey>

    /**
     * Retrieves a single API key configuration by its unique identifier.
     *
     * @param id The unique identifier of the API key to retrieve.
     * @return [Either] a [GetApiKeyError.ApiKeyNotFound] if the API key doesn't exist, or the [ApiKey].
     */
    suspend fun getApiKeyById(id: String): Either<GetApiKeyError.ApiKeyNotFound, ApiKey>

    /**
     * Adds a new API key configuration.
     * Creates a secure credential entry and associates it with the provided metadata.
     *
     * @param name The display name for the API key.
     * @param description The description for the API key.
     * @param credential The actual API key credential to store securely.
     * @return Either an [AddApiKeyError], or the newly created [ApiKey].
     */
    suspend fun addApiKey(name: String, description: String, credential: String): Either<AddApiKeyError, ApiKey>

    /**
     * Updates an existing API key configuration.
     * Note: This only updates the metadata (name, description). The credential itself cannot be updated.
     *
     * @param apiKey The ApiKey object containing the updated values. The ID must match an existing API key.
     * @return Either an [UpdateApiKeyError] or Unit if successful.
     */
    suspend fun updateApiKey(apiKey: ApiKey): Either<UpdateApiKeyError, Unit>

    /**
     * Deletes an API key configuration and its associated credential.
     * Checks if the API key is still in use by any models before deletion.
     *
     * @param id The ID of the API key to delete.
     * @return Either a [DeleteApiKeyError], or Unit if successful.
     */
    suspend fun deleteApiKey(id: String): Either<DeleteApiKeyError, Unit>
}
