package eu.torvian.chatbot.server.data.dao.exposed

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import eu.torvian.chatbot.common.models.Api<PERSON>ey
import eu.torvian.chatbot.server.data.dao.ApiKeyDao
import eu.torvian.chatbot.server.data.dao.error.ApiKeyError
import eu.torvian.chatbot.server.data.tables.ApiKeyTable
import eu.torvian.chatbot.server.utils.transactions.TransactionScope
import org.jetbrains.exposed.sql.*
import org.jetbrains.exposed.sql.SqlExpressionBuilder.eq

/**
 * Exposed implementation of the [ApiKeyDao].
 */
class ApiKeyDaoExposed(
    private val transactionScope: TransactionScope
) : ApiKeyDao {

    override suspend fun getAllApiKeys(): List<ApiKey> =
        transactionScope.transaction {
            ApiKeyTable.selectAll()
                .map { it.toApiKey() }
        }

    override suspend fun getApiKeyById(id: String): Either<ApiKeyError.ApiKeyNotFound, ApiKey> =
        transactionScope.transaction {
            ApiKeyTable.selectAll().where { ApiKeyTable.id eq id }
                .singleOrNull()
                ?.toApiKey()
                ?.right()
                ?: ApiKeyError.ApiKeyNotFound(id).left()
        }

    override suspend fun insertApiKey(
        id: String,
        name: String,
        description: String
    ): ApiKey =
        transactionScope.transaction {
            val insertStatement = ApiKeyTable.insert {
                it[ApiKeyTable.id] = id
                it[ApiKeyTable.name] = name
                it[ApiKeyTable.description] = description
            }

            if (insertStatement.insertedCount == 0) {
                throw IllegalStateException("Failed to insert API key")
            }

            ApiKey(id = id, name = name, description = description)
        }

    override suspend fun updateApiKey(apiKey: ApiKey): Either<ApiKeyError.ApiKeyNotFound, Unit> =
        transactionScope.transaction {
            val updatedRows = ApiKeyTable.update({ ApiKeyTable.id eq apiKey.id }) {
                it[name] = apiKey.name
                it[description] = apiKey.description
            }
            
            if (updatedRows == 0) {
                ApiKeyError.ApiKeyNotFound(apiKey.id).left()
            } else {
                Unit.right()
            }
        }

    override suspend fun deleteApiKey(id: String): Either<ApiKeyError.ApiKeyNotFound, Unit> =
        transactionScope.transaction {
            val deletedRows = ApiKeyTable.deleteWhere { ApiKeyTable.id eq id }
            
            if (deletedRows == 0) {
                ApiKeyError.ApiKeyNotFound(id).left()
            } else {
                Unit.right()
            }
        }

    /**
     * Extension function to convert a ResultRow to an ApiKey.
     */
    private fun ResultRow.toApiKey(): ApiKey = ApiKey(
        id = this[ApiKeyTable.id],
        name = this[ApiKeyTable.name],
        description = this[ApiKeyTable.description]
    )
}
