package eu.torvian.chatbot.server.service.error.provider

/**
 * Represents possible errors when adding a new LLM provider.
 */
sealed interface AddProviderError {
    /**
     * Indicates invalid input data for the provider (e.g., name, description, or baseUrl format).
     * This would typically be a business rule validation failure.
     */
    data class InvalidInput(val reason: String) : AddProviderError
    
    /**
     * Indicates that storing the credential failed.
     * This could happen if the credential storage system is unavailable.
     */
    data class CredentialStorageFailure(val reason: String) : AddProviderError
    
    /**
     * Indicates that a provider with the specified API key already exists.
     * This can happen if there's a collision in the credential alias generation.
     */
    data class ApiKeyAlreadyInUse(val apiKeyId: String) : AddProviderError
}
