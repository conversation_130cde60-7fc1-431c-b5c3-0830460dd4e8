package eu.torvian.chatbot.server.service.impl

import arrow.core.Either
import arrow.core.*
import arrow.core.raise.*
import arrow.core.raise.ensure
import eu.torvian.chatbot.common.models.ApiKey
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.data.dao.ApiKeyDao
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.SettingsDao
import eu.torvian.chatbot.server.data.dao.error.ApiKeyError
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.SettingsError
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.error.apikey.*
import eu.torvian.chatbot.server.service.error.model.*
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [ModelService] interface.
 */
class ModelServiceImpl(
    private val modelDao: ModelDao,
    private val settingsDao: SettingsDao,
    private val apiKeyDao: ApiKeyDao,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : ModelService {

    override suspend fun getAllModels(): List<LLMModel> {
        return transactionScope.transaction {
            modelDao.getAllModels()
        }
    }

    override suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    GetModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }
            }
        }

    override suspend fun addModel(name: String, baseUrl: String, type: String, apiKeyId: String?): Either<AddModelError, LLMModel> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddModelError.InvalidInput("Model name cannot be blank.")
                }
                ensure(!baseUrl.isBlank()) {
                    AddModelError.InvalidInput("Model base URL cannot be blank.")
                }
                ensure(!type.isBlank()) {
                    AddModelError.InvalidInput("Model type cannot be blank.")
                }

                if (apiKeyId != null) {
                    withError({ credError: CredentialError.CredentialNotFound ->
                        AddModelError.CredentialNotFound(credError.alias)
                    }) {
                        credentialManager.getCredential(apiKeyId).bind()
                    }
                }

                modelDao.insertModel(name, baseUrl, type, apiKeyId)
            }
        }

    override suspend fun updateModel(model: LLMModel): Either<UpdateModelError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!model.name.isBlank()) {
                    UpdateModelError.InvalidInput("Name cannot be blank.")
                }
                ensure(!model.baseUrl.isBlank()) {
                    UpdateModelError.InvalidInput("Base URL cannot be blank.")
                }
                ensure(!model.type.isBlank()) {
                    UpdateModelError.InvalidInput("Type cannot be blank.")
                }

                model.apiKeyId?.let { keyId ->
                    withError({ credError: CredentialError.CredentialNotFound ->
                        UpdateModelError.CredentialNotFound(credError.alias)
                    }) {
                        credentialManager.getCredential(keyId).bind()
                    }
                }

                withError({ daoError: ModelError.ModelNotFound ->
                    UpdateModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.updateModel(model).bind()
                }
            }
        }

    override suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.deleteModel(id).bind()
                }
            }
        }

    override suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    GetSettingsByIdError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }
            }
        }

    override suspend fun getAllSettings(): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getAllSettings()
        }
    }

    override suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getSettingsByModelId(modelId)
        }
    }

    override suspend fun addSettings(
        name: String, modelId: Long, systemMessage: String?,
        temperature: Float?, maxTokens: Int?, customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    AddSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    AddSettingsError.InvalidInput("Max tokens must be positive")
                }

                withError({ daoError: SettingsError.ModelNotFound ->
                    AddSettingsError.ModelNotFound(daoError.modelId)
                }) {
                    settingsDao.insertSettings(name, modelId, systemMessage, temperature, maxTokens, customParamsJson).bind()
                }
            }
        }

    override suspend fun updateSettings(settings: ModelSettings): Either<UpdateSettingsError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!settings.name.isBlank()) {
                    UpdateSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                val temperature = settings.temperature
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    UpdateSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                val maxTokens = settings.maxTokens
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    UpdateSettingsError.InvalidInput("Max tokens must be positive")
                }

                withError({ daoError: SettingsError ->
                    when(daoError) {
                        is SettingsError.SettingsNotFound -> UpdateSettingsError.SettingsNotFound(daoError.id)
                        is SettingsError.ModelNotFound -> UpdateSettingsError.ModelNotFound(daoError.modelId)
                    }
                }) {
                    settingsDao.updateSettings(settings).bind()
                }
            }
        }

    override suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    DeleteSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.deleteSettings(id).bind()
                }
            }
        }

    override suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean {
        return transactionScope.transaction {
            modelDao.getModelById(modelId)
                .map { it.apiKeyId != null }
                .getOrElse { false }
        }
    }

    // --- API Key Management Implementation ---

    override suspend fun getAllApiKeys(): List<ApiKey> {
        return transactionScope.transaction {
            apiKeyDao.getAllApiKeys()
        }
    }

    override suspend fun getApiKeyById(id: String): Either<GetApiKeyError.ApiKeyNotFound, ApiKey> =
        transactionScope.transaction {
            either {
                withError({ daoError: ApiKeyError.ApiKeyNotFound ->
                    GetApiKeyError.ApiKeyNotFound(daoError.id)
                }) {
                    apiKeyDao.getApiKeyById(id).bind()
                }
            }
        }

    override suspend fun addApiKey(name: String, description: String, credential: String): Either<AddApiKeyError, ApiKey> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddApiKeyError.InvalidInput("API key name cannot be blank.")
                }
                ensure(!credential.isBlank()) {
                    AddApiKeyError.InvalidInput("API key credential cannot be blank.")
                }

                // Store the credential securely and get the alias
                val alias = try {
                    credentialManager.storeCredential(credential)
                } catch (e: Exception) {
                    raise(AddApiKeyError.CredentialStorageFailure("Failed to store credential: ${e.message}"))
                }

                // Create the API key metadata entry
                apiKeyDao.insertApiKey(alias, name, description)
            }
        }

    override suspend fun updateApiKey(apiKey: ApiKey): Either<UpdateApiKeyError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!apiKey.name.isBlank()) {
                    UpdateApiKeyError.InvalidInput("API key name cannot be blank.")
                }

                withError({ daoError: ApiKeyError.ApiKeyNotFound ->
                    UpdateApiKeyError.ApiKeyNotFound(daoError.id)
                }) {
                    apiKeyDao.updateApiKey(apiKey).bind()
                }
            }
        }

    override suspend fun deleteApiKey(id: String): Either<DeleteApiKeyError, Unit> =
        transactionScope.transaction {
            either {
                // Check if the API key is still in use by any models
                val modelsUsingKey = modelDao.getAllModels().filter { it.apiKeyId == id }
                ensure(modelsUsingKey.isEmpty()) {
                    DeleteApiKeyError.ApiKeyInUse(id, modelsUsingKey.map { it.name })
                }

                // Delete the API key metadata
                withError({ daoError: ApiKeyError.ApiKeyNotFound ->
                    DeleteApiKeyError.ApiKeyNotFound(daoError.id)
                }) {
                    apiKeyDao.deleteApiKey(id).bind()
                }

                // Delete the associated credential
                credentialManager.deleteCredential(id).getOrElse {
                    // Log warning but don't fail the operation if credential is already gone
                    // This could happen if the credential was manually deleted
                }
            }
        }
}
