package eu.torvian.chatbot.server.service.impl

import arrow.core.Either
import arrow.core.*
import arrow.core.raise.*
import arrow.core.raise.ensure
import eu.torvian.chatbot.common.models.LLMModel
import eu.torvian.chatbot.common.models.LLMProvider
import eu.torvian.chatbot.common.models.LLMProviderType
import eu.torvian.chatbot.common.models.ModelSettings
import eu.torvian.chatbot.server.data.dao.LLMProviderDao
import eu.torvian.chatbot.server.data.dao.ModelDao
import eu.torvian.chatbot.server.data.dao.SettingsDao
import eu.torvian.chatbot.server.data.dao.error.InsertModelError
import eu.torvian.chatbot.server.data.dao.error.LLMProviderError
import eu.torvian.chatbot.server.data.dao.error.ModelError
import eu.torvian.chatbot.server.data.dao.error.SettingsError
import eu.torvian.chatbot.server.data.dao.error.UpdateModelError as DaoUpdateModelError
import eu.torvian.chatbot.server.data.dao.error.UpdateProviderError as DaoUpdateProviderError
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.error.model.AddModelError
import eu.torvian.chatbot.server.service.error.model.DeleteModelError
import eu.torvian.chatbot.server.service.error.model.GetModelError
import eu.torvian.chatbot.server.service.error.model.UpdateModelError
import eu.torvian.chatbot.server.service.error.provider.AddProviderError
import eu.torvian.chatbot.server.service.error.provider.DeleteProviderError
import eu.torvian.chatbot.server.service.error.provider.GetProviderError
import eu.torvian.chatbot.server.service.error.provider.UpdateProviderError
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.security.CredentialManager
import eu.torvian.chatbot.server.service.security.error.CredentialError
import eu.torvian.chatbot.server.utils.transactions.TransactionScope

/**
 * Implementation of the [ModelService] interface.
 */
class ModelServiceImpl(
    private val modelDao: ModelDao,
    private val settingsDao: SettingsDao,
    private val llmProviderDao: LLMProviderDao,
    private val credentialManager: CredentialManager,
    private val transactionScope: TransactionScope,
) : ModelService {

    override suspend fun getAllModels(): List<LLMModel> {
        return transactionScope.transaction {
            modelDao.getAllModels()
        }
    }

    override suspend fun getModelById(id: Long): Either<GetModelError.ModelNotFound, LLMModel> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    GetModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.getModelById(id).bind()
                }
            }
        }

    override suspend fun getModelsByProviderId(providerId: Long): List<LLMModel> =
        transactionScope.transaction {
            modelDao.getModelsByProviderId(providerId)
        }

    override suspend fun addModel(name: String, providerId: Long, active: Boolean, displayName: String?): Either<AddModelError, LLMModel> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddModelError.InvalidInput("Model name cannot be blank.")
                }

                // Verify the provider exists by trying to get it
                withError({ daoError: LLMProviderError.LLMProviderNotFound ->
                    AddModelError.ProviderNotFound(daoError.id)
                }) {
                    llmProviderDao.getProviderById(providerId).bind()
                }

                withError({ daoError: InsertModelError ->
                    when (daoError) {
                        is InsertModelError.ProviderNotFound -> AddModelError.ProviderNotFound(daoError.providerId)
                        is InsertModelError.ModelNameAlreadyExists -> AddModelError.ModelNameAlreadyExists(daoError.name)
                    }
                }) {
                    modelDao.insertModel(name, providerId, active, displayName).bind()
                }
            }
        }

    override suspend fun updateModel(model: LLMModel): Either<UpdateModelError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!model.name.isBlank()) {
                    UpdateModelError.InvalidInput("Model name cannot be blank.")
                }

                // Verify the provider exists by trying to get it
                withError({ daoError: LLMProviderError.LLMProviderNotFound ->
                    UpdateModelError.ProviderNotFound(daoError.id)
                }) {
                    llmProviderDao.getProviderById(model.providerId).bind()
                }

                withError({ daoError: DaoUpdateModelError ->
                    when (daoError) {
                        is DaoUpdateModelError.ModelNotFound -> UpdateModelError.ModelNotFound(daoError.id)
                        is DaoUpdateModelError.ProviderNotFound -> UpdateModelError.ProviderNotFound(daoError.providerId)
                        is DaoUpdateModelError.ModelNameAlreadyExists -> UpdateModelError.ModelNameAlreadyExists(daoError.name)
                    }
                }) {
                    modelDao.updateModel(model).bind()
                }
            }
        }

    override suspend fun deleteModel(id: Long): Either<DeleteModelError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: ModelError.ModelNotFound ->
                    DeleteModelError.ModelNotFound(daoError.id)
                }) {
                    modelDao.deleteModel(id).bind()
                }
            }
        }

    override suspend fun getSettingsById(id: Long): Either<GetSettingsByIdError, ModelSettings> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    GetSettingsByIdError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.getSettingsById(id).bind()
                }
            }
        }

    override suspend fun getAllSettings(): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getAllSettings()
        }
    }

    override suspend fun getSettingsByModelId(modelId: Long): List<ModelSettings> {
        return transactionScope.transaction {
            settingsDao.getSettingsByModelId(modelId)
        }
    }

    override suspend fun addSettings(
        name: String, modelId: Long, systemMessage: String?,
        temperature: Float?, maxTokens: Int?, customParamsJson: String?
    ): Either<AddSettingsError, ModelSettings> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    AddSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    AddSettingsError.InvalidInput("Max tokens must be positive")
                }

                withError({ daoError: SettingsError.ModelNotFound ->
                    AddSettingsError.ModelNotFound(daoError.modelId)
                }) {
                    settingsDao.insertSettings(name, modelId, systemMessage, temperature, maxTokens, customParamsJson).bind()
                }
            }
        }

    override suspend fun updateSettings(settings: ModelSettings): Either<UpdateSettingsError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!settings.name.isBlank()) {
                    UpdateSettingsError.InvalidInput("Settings name cannot be blank.")
                }
                val temperature = settings.temperature
                ensure(!(temperature != null && (temperature < 0f || temperature > 2f))) {
                    UpdateSettingsError.InvalidInput("Temperature must be between 0.0 and 2.0")
                }
                val maxTokens = settings.maxTokens
                ensure(!(maxTokens != null && maxTokens <= 0)) {
                    UpdateSettingsError.InvalidInput("Max tokens must be positive")
                }

                withError({ daoError: SettingsError ->
                    when(daoError) {
                        is SettingsError.SettingsNotFound -> UpdateSettingsError.SettingsNotFound(daoError.id)
                        is SettingsError.ModelNotFound -> UpdateSettingsError.ModelNotFound(daoError.modelId)
                    }
                }) {
                    settingsDao.updateSettings(settings).bind()
                }
            }
        }

    override suspend fun deleteSettings(id: Long): Either<DeleteSettingsError, Unit> =
        transactionScope.transaction {
            either {
                withError({ daoError: SettingsError.SettingsNotFound ->
                    DeleteSettingsError.SettingsNotFound(daoError.id)
                }) {
                    settingsDao.deleteSettings(id).bind()
                }
            }
        }

    override suspend fun isApiKeyConfiguredForModel(modelId: Long): Boolean {
        return transactionScope.transaction {
            modelDao.getModelById(modelId)
                .map { model ->
                    // Get the provider and check if it has an API key
                    llmProviderDao.getProviderById(model.providerId)
                        .map { provider -> provider.apiKeyId.isNotBlank() }
                        .getOrElse { false }
                }
                .getOrElse { false }
        }
    }

    // --- LLM Provider Management Implementation ---

    override suspend fun getAllProviders(): List<LLMProvider> {
        return transactionScope.transaction {
            llmProviderDao.getAllProviders()
        }
    }

    override suspend fun getProviderById(id: Long): Either<GetProviderError.ProviderNotFound, LLMProvider> =
        transactionScope.transaction {
            either {
                withError({ daoError: LLMProviderError.LLMProviderNotFound ->
                    GetProviderError.ProviderNotFound(daoError.id)
                }) {
                    llmProviderDao.getProviderById(id).bind()
                }
            }
        }

    override suspend fun addProvider(name: String, description: String, baseUrl: String, type: LLMProviderType, credential: String): Either<AddProviderError, LLMProvider> =
        transactionScope.transaction {
            either {
                ensure(!name.isBlank()) {
                    AddProviderError.InvalidInput("Provider name cannot be blank.")
                }
                ensure(!baseUrl.isBlank()) {
                    AddProviderError.InvalidInput("Provider base URL cannot be blank.")
                }
                ensure(!credential.isBlank()) {
                    AddProviderError.InvalidInput("Provider credential cannot be blank.")
                }

                // Store the credential securely and get the alias
                val alias = try {
                    credentialManager.storeCredential(credential)
                } catch (e: Exception) {
                    raise(AddProviderError.CredentialStorageFailure("Failed to store credential: ${e.message}"))
                }

                // Create the provider metadata entry
                withError({ daoError: LLMProviderError.ApiKeyAlreadyInUse ->
                    AddProviderError.ApiKeyAlreadyInUse(daoError.apiKeyId)
                }) {
                    llmProviderDao.insertProvider(alias, name, description, baseUrl, type).bind()
                }
            }
        }

    override suspend fun updateProvider(provider: LLMProvider): Either<UpdateProviderError, Unit> =
        transactionScope.transaction {
            either {
                ensure(!provider.name.isBlank()) {
                    UpdateProviderError.InvalidInput("Provider name cannot be blank.")
                }
                ensure(!provider.baseUrl.isBlank()) {
                    UpdateProviderError.InvalidInput("Provider base URL cannot be blank.")
                }

                withError({ daoError: DaoUpdateProviderError ->
                    when (daoError) {
                        is DaoUpdateProviderError.ProviderNotFound -> UpdateProviderError.ProviderNotFound(daoError.id)
                        is DaoUpdateProviderError.ApiKeyAlreadyInUse -> UpdateProviderError.ApiKeyAlreadyInUse(daoError.apiKeyId)
                    }
                }) {
                    llmProviderDao.updateProvider(provider).bind()
                }
            }
        }

    override suspend fun deleteProvider(id: Long): Either<DeleteProviderError, Unit> =
        transactionScope.transaction {
            either {
                // Check if the provider is still in use by any models
                val modelsUsingProvider = modelDao.getModelsByProviderId(id)
                ensure(modelsUsingProvider.isEmpty()) {
                    DeleteProviderError.ProviderInUse(id, modelsUsingProvider.map { it.name })
                }

                // Get the provider to get the API key ID for credential deletion
                val provider = withError({ daoError: LLMProviderError.LLMProviderNotFound ->
                    DeleteProviderError.ProviderNotFound(daoError.id)
                }) {
                    llmProviderDao.getProviderById(id).bind()
                }

                // Delete the provider metadata
                withError({ daoError: LLMProviderError.LLMProviderNotFound ->
                    DeleteProviderError.ProviderNotFound(daoError.id)
                }) {
                    llmProviderDao.deleteProvider(id).bind()
                }

                // Delete the associated credential
                credentialManager.deleteCredential(provider.apiKeyId).getOrElse {
                    // Log warning but don't fail the operation if credential is already gone
                    // This could happen if the credential was manually deleted
                }
            }
        }
}
