package eu.torvian.chatbot.server.ktor

import arrow.core.Either
import eu.torvian.chatbot.common.models.*
import eu.torvian.chatbot.server.service.GroupService
import eu.torvian.chatbot.server.service.MessageService
import eu.torvian.chatbot.server.service.ModelService
import eu.torvian.chatbot.server.service.SessionService
import eu.torvian.chatbot.server.service.error.group.*
import eu.torvian.chatbot.server.service.error.message.*
import eu.torvian.chatbot.server.service.error.model.*
import eu.torvian.chatbot.server.service.error.provider.*
import eu.torvian.chatbot.server.service.error.settings.*
import eu.torvian.chatbot.server.service.error.session.*

import io.ktor.http.HttpStatusCode
import io.ktor.server.application.*
import io.ktor.server.request.*
import io.ktor.server.response.*
import io.ktor.server.routing.*

/**
 * Extension function to respond with the result of an Either, mapping Left to an error and Right to success.
 */
private suspend inline fun <reified R : Any, reified L : Any> ApplicationCall.respondEither(
    either: Either<L, R>,
    successCode: HttpStatusCode = HttpStatusCode.OK,
    noinline errorMapping: (L) -> Pair<HttpStatusCode, String> = { HttpStatusCode.InternalServerError to it.toString() }
) {
    when (either) {
        is Either.Right -> respond(successCode, either.value)
        is Either.Left -> {
            val (status, message) = errorMapping(either.value)
            // Log the error on the server side for debugging
            // application.environment.log.error("API Error: $message (Status: $status, Details: $either)")
            respond(status, message)
        }
    }
}

/**
 * Configures the Ktor server routing for the application API (v1).
 *
 * @param sessionService The injected SessionService instance.
 * @param groupService The injected GroupService instance.
 * @param modelService The injected ModelService instance.
 * @param messageService The injected MessageService instance.
 */
fun Application.configureRouting(
    sessionService: SessionService,
    groupService: GroupService,
    modelService: ModelService,
    messageService: MessageService,
) {
    routing {
        route("/api/v1") {
            // --- Session Routes ---
            route("/sessions") {
                get {
                    call.respond(sessionService.getAllSessionsSummaries())
                }
                post {
                    val request = call.receive<CreateSessionRequest>()
                    call.respondEither(sessionService.createSession(request.name), HttpStatusCode.Created) {
                        when (it) {
                            is CreateSessionError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                            is CreateSessionError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to it.message // Assuming client sends invalid FK ID
                        }
                    }
                }
                route("/{sessionId}") {
                    get {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        call.respondEither(sessionService.getSessionDetails(sessionId)) {
                            when (it) {
                                is GetSessionDetailsError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                            }
                        }
                    }

                    // Granular PUT routes for specific updates
                    put("/name") {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                         val request = call.receive<UpdateSessionNameRequest>()
                         call.respondEither(sessionService.updateSessionName(sessionId, request.name), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionNameError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionNameError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                             }
                         }
                    }
                    put("/model") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionModelRequest>()
                        call.respondEither(sessionService.updateSessionCurrentModelId(sessionId, request.modelId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionCurrentModelIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionCurrentModelIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid model ID provided: ${request.modelId}"
                             }
                        }
                    }
                     put("/settings") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionSettingsRequest>()
                        call.respondEither(sessionService.updateSessionCurrentSettingsId(sessionId, request.settingsId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionCurrentSettingsIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionCurrentSettingsIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid settings ID provided: ${request.settingsId}"
                             }
                        }
                    }
                    put("/leafMessage") {
                         val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                         val request = call.receive<UpdateSessionLeafMessageRequest>()
                         call.respondEither(sessionService.updateSessionLeafMessageId(sessionId, request.leafMessageId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionLeafMessageIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionLeafMessageIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid leaf message ID provided: ${request.leafMessageId}"
                             }
                         }
                    }

                    // Specific endpoint for assigning/ungrouping
                    put("/group") {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        val request = call.receive<UpdateSessionGroupRequest>()
                        
                        call.respondEither(sessionService.updateSessionGroupId(sessionId, request.groupId), HttpStatusCode.OK) {
                             when(it) {
                                 is UpdateSessionGroupIdError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                                 is UpdateSessionGroupIdError.InvalidRelatedEntity -> HttpStatusCode.BadRequest to "Invalid group ID provided: ${request.groupId}"
                             }
                        }
                    }

                    delete {
                        val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                        call.respondEither(sessionService.deleteSession(sessionId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteSessionError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.id}"
                            }
                        }
                    }
                } // End sessions/{sessionId}
            } // End /sessions

            // --- Group Routes ---
            route("/groups") {
                get {
                    call.respond(groupService.getAllGroups())
                }
                post {
                    val request = call.receive<CreateGroupRequest>()
                    call.respondEither(groupService.createGroup(request.name), HttpStatusCode.Created) {
                         when(it) {
                            is CreateGroupError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                            // Add mapping for AlreadyExists if implemented later
                         }
                    }
                }
                route("/{groupId}") {
                    delete {
                        val groupId = call.parameters["groupId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid group ID")
                        call.respondEither(groupService.deleteGroup(groupId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteGroupError.GroupNotFound -> HttpStatusCode.NotFound to "Group not found: ${it.id}"
                            }
                        }
                    }
                    put {
                        val groupId = call.parameters["groupId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid group ID")
                        val request = call.receive<RenameGroupRequest>()
                        call.respondEither(groupService.renameGroup(groupId, request.name), HttpStatusCode.OK) {
                             when(it) {
                                 is RenameGroupError.GroupNotFound -> HttpStatusCode.NotFound to "Group not found: ${it.id}"
                                 is RenameGroupError.InvalidName -> HttpStatusCode.BadRequest to it.reason
                             }
                        }
                    }
                    
                }
            }

            // --- Provider Routes ---
            route("/providers") {
                get { // List all providers
                    call.respond(modelService.getAllProviders())
                }
                post { // Add new provider
                    val request = call.receive<AddProviderRequest>()
                    call.respondEither(modelService.addProvider(request.name, request.description, request.baseUrl, request.type, request.credential), HttpStatusCode.Created) {
                        when(it) {
                            is AddProviderError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                            is AddProviderError.CredentialStorageFailure -> HttpStatusCode.InternalServerError to it.reason
                            is AddProviderError.ApiKeyAlreadyInUse -> HttpStatusCode.Conflict to "API key already in use: ${it.apiKeyId}"
                        }
                    }
                }
                route("/{providerId}") {
                    get { // Get provider by ID
                        val providerId = call.parameters["providerId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid provider ID")
                        call.respondEither(modelService.getProviderById(providerId)) {
                            when(it) {
                                is GetProviderError.ProviderNotFound -> HttpStatusCode.NotFound to "Provider not found: ${it.id}"
                            }
                        }
                    }
                    put { // Update provider by ID
                        val providerId = call.parameters["providerId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid provider ID")
                        val provider = call.receive<LLMProvider>()

                        if (provider.id != providerId) {
                            return@put call.respond(HttpStatusCode.BadRequest, "Provider ID in path and body must match")
                        }
                        call.respondEither(modelService.updateProvider(provider)) {
                            when(it) {
                                is UpdateProviderError.ProviderNotFound -> HttpStatusCode.NotFound to "Provider not found: ${it.id}"
                                is UpdateProviderError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                is UpdateProviderError.ApiKeyAlreadyInUse -> HttpStatusCode.Conflict to "API key already in use: ${it.apiKeyId}"
                            }
                        }
                    }
                    delete { // Delete provider by ID
                        val providerId = call.parameters["providerId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest,"Invalid provider ID")
                        call.respondEither(modelService.deleteProvider(providerId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteProviderError.ProviderNotFound -> HttpStatusCode.NotFound to "Provider not found: ${it.id}"
                                is DeleteProviderError.ProviderInUse -> HttpStatusCode.Conflict to "Provider is still in use by models: ${it.modelNames.joinToString(", ")}"
                            }
                        }
                    }

                    // Update provider credential
                    route("/credential") {
                        put { // Update provider credential
                            val providerId = call.parameters["providerId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid provider ID")
                            val request = call.receive<UpdateProviderCredentialRequest>()
                            call.respondEither(modelService.updateProviderCredential(providerId, request.credential), HttpStatusCode.NoContent) {
                                when(it) {
                                    is UpdateProviderCredentialError.ProviderNotFound -> HttpStatusCode.NotFound to "Provider not found: ${it.id}"
                                    is UpdateProviderCredentialError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                    is UpdateProviderCredentialError.ApiKeyAlreadyInUse -> HttpStatusCode.Conflict to "API key already in use: ${it.apiKeyId}"
                                }
                            }
                        }
                    }

                    // Get models for this provider
                    route("/models") {
                        get { // Get models by provider ID
                            val providerId = call.parameters["providerId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid provider ID")
                            call.respond(modelService.getModelsByProviderId(providerId))
                        }
                    }
                } // End /providers/{providerId}
            } // End /providers

            // --- Model Routes ---
            route("/models") {
                 get { // List all models
                     call.respond(modelService.getAllModels())
                 }
                 post { // Add new model
                     val request = call.receive<AddModelRequest>()
                     call.respondEither(modelService.addModel(request.name, request.providerId, request.active, request.displayName), HttpStatusCode.Created) {
                         when(it) {
                             is AddModelError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                             is AddModelError.ProviderNotFound -> HttpStatusCode.BadRequest to "Provider not found: ${it.providerId}"
                             is AddModelError.ModelNameAlreadyExists -> HttpStatusCode.Conflict to "Model name already exists: ${it.name}"
                         }
                     }
                 }
                 route("/{modelId}") {
                     get { // Get model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         call.respondEither(modelService.getModelById(modelId)) {
                             when(it) {
                                 is GetModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                             }
                         }
                     }
                     put { // Update model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         val model = call.receive<LLMModel>()

                         if (model.id != modelId) {
                             return@put call.respond(HttpStatusCode.BadRequest, "Model ID in path and body must match")
                         }
                         call.respondEither(modelService.updateModel(model)) {
                             when(it) {
                                 is UpdateModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                                 is UpdateModelError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                 is UpdateModelError.ProviderNotFound -> HttpStatusCode.BadRequest to "Provider not found: ${it.providerId}"
                                 is UpdateModelError.ModelNameAlreadyExists -> HttpStatusCode.Conflict to "Model name already exists: ${it.name}"
                             }
                         }
                     }
                     delete { // Delete model by ID
                         val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                         call.respondEither(modelService.deleteModel(modelId), HttpStatusCode.NoContent) {
                             when(it) {
                                 is DeleteModelError.ModelNotFound -> HttpStatusCode.NotFound to "Model not found: ${it.id}"
                             }
                         }
                     }

                     // Nested settings routes under model
                     route("/settings") {
                         get { // List settings for this model
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             call.respond(modelService.getSettingsByModelId(modelId)) // Service doesn't use Either for this List retrieval
                         }
                         post { // Add new settings for this model
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@post call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             val request = call.receive<AddModelSettingsRequest>()
                             call.respondEither(
                                 modelService.addSettings(
                                     request.name,
                                     modelId,
                                     request.systemMessage,
                                     request.temperature,
                                     request.maxTokens,
                                     request.customParamsJson
                                 ), HttpStatusCode.Created
                             ) {
                                 when(it) {
                                     is AddSettingsError.ModelNotFound -> HttpStatusCode.BadRequest to "Model not found for settings: ${it.modelId}" // Mapped from FK violation
                                     is AddSettingsError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                 }
                             }
                         }
                     }

                     // API Key Status for a model (existing route)
                     route("/apikey/status") {
                         get {
                             val modelId = call.parameters["modelId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid model ID")
                             val isConfigured = modelService.isApiKeyConfiguredForModel(modelId)
                             call.respond(ApiKeyStatusResponse(isConfigured))
                         }
                     }
                 } // End /models/{modelId}
             } // End /models

            // --- Settings Routes (top-level for getting/updating/deleting specific settings) ---
            route("/settings") {
                route("/{settingsId}") {
                    get { // Get settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@get call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        call.respondEither(modelService.getSettingsById(settingsId)) {
                            when(it) {
                                is GetSettingsByIdError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                            }
                        }
                    }
                    put { // Update settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        val settings = call.receive<ModelSettings>()
                        if (settings.id != settingsId) {
                            return@put call.respond(HttpStatusCode.BadRequest, "Settings ID in path and body must match")
                        }
                        call.respondEither(modelService.updateSettings(settings)) {
                            when(it) {
                                is UpdateSettingsError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                                is UpdateSettingsError.InvalidInput -> HttpStatusCode.BadRequest to it.reason
                                is UpdateSettingsError.ModelNotFound -> HttpStatusCode.BadRequest to "Model not found for settings: ${it.modelId}"
                            }
                        }
                    }
                    delete { // Delete settings by ID
                        val settingsId = call.parameters["settingsId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest,"Invalid settings ID")
                        call.respondEither(modelService.deleteSettings(settingsId), HttpStatusCode.NoContent) {
                            when(it) {
                                is DeleteSettingsError.SettingsNotFound -> HttpStatusCode.NotFound to "Settings not found: ${it.id}"
                            }
                        }
                    }
                } // End /settings/{settingsId}
            } // End /settings


            // --- Message Routes ---
             route("/sessions/{sessionId}/messages") {
                 post { // Process a new message
                     val sessionId = call.parameters["sessionId"]?.toLongOrNull() ?: return@post call.respond(HttpStatusCode.BadRequest, "Invalid session ID")
                     val request = call.receive<ProcessNewMessageRequest>()
                     call.respondEither(messageService.processNewMessage(sessionId, request.content, request.parentMessageId), HttpStatusCode.Created) {
                         when(it) {
                             is ProcessNewMessageError.SessionNotFound -> HttpStatusCode.NotFound to "Session not found: ${it.sessionId}"
                             is ProcessNewMessageError.ParentNotInSession -> HttpStatusCode.BadRequest to "Parent message does not belong to this session (parent: ${it.parentId}, session: ${it.sessionId})"
                             is ProcessNewMessageError.ModelConfigurationError -> HttpStatusCode.BadRequest to "LLM configuration error: ${it.message}"
                             is ProcessNewMessageError.ExternalServiceError -> HttpStatusCode.InternalServerError to "LLM API Error: ${it.message}"
                         }
                     }
                 }
             }

             route("/messages/{messageId}") {
                 put("/content") {
                     val messageId = call.parameters["messageId"]?.toLongOrNull() ?: return@put call.respond(HttpStatusCode.BadRequest, "Invalid message ID")
                     val request = call.receive<UpdateMessageRequest>()
                     call.respondEither(messageService.updateMessageContent(messageId, request.content), HttpStatusCode.OK) {
                         when(it) {
                             is UpdateMessageContentError.MessageNotFound -> HttpStatusCode.NotFound to "Message not found: ${it.id}"
                         }
                     }
                 }
                 delete {
                     val messageId = call.parameters["messageId"]?.toLongOrNull() ?: return@delete call.respond(HttpStatusCode.BadRequest, "Invalid message ID")
                     call.respondEither(messageService.deleteMessage(messageId), HttpStatusCode.NoContent) {
                         when(it) {
                             is DeleteMessageError.MessageNotFound -> HttpStatusCode.NotFound to "Message not found: ${it.id}" 
                         }
                     }
                 }
             } // End /messages/{messageId}

        } // End /api/v1
    }
}