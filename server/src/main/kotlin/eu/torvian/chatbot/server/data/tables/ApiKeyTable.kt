package eu.torvian.chatbot.server.data.tables

import eu.torvian.chatbot.common.models.ApiKey
import org.jetbrains.exposed.sql.Table

/**
 * Exposed table definition for API key configurations.
 * Corresponds to the [ApiKey] DTO.
 *
 * This table stores metadata about API keys, while the actual credentials
 * are stored securely in the ApiSecretTable via the CredentialManager system.
 *
 * @property id The unique identifier (primary key) that references ApiSecretEntity.alias
 * @property name The display name for the API key (not unique)
 * @property description Optional description providing context about the API key
 */
object ApiKeyTable : Table("api_keys") {
    val id = varchar("id", 255)
    val name = varchar("name", 255)
    val description = text("description")
    
    override val primaryKey = PrimaryKey(id)
    
    // Foreign key reference to ApiSecretTable
    init {
        foreignKey(id to ApiSecretTable.alias)
    }
}
