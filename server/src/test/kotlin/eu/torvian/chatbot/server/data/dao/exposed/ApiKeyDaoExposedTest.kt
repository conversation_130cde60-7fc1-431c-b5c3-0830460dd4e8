package eu.torvian.chatbot.server.data.dao.exposed

import arrow.core.getOrElse
import eu.torvian.chatbot.common.misc.di.DIContainer
import eu.torvian.chatbot.common.misc.di.get
import eu.torvian.chatbot.common.models.Api<PERSON>ey
import eu.torvian.chatbot.server.data.dao.ApiKeyDao
import eu.torvian.chatbot.server.data.dao.error.ApiKeyError
import eu.torvian.chatbot.server.data.entities.ApiSecretEntity
import eu.torvian.chatbot.server.testutils.data.Table
import eu.torvian.chatbot.server.testutils.data.TestDataManager
import eu.torvian.chatbot.server.testutils.data.TestDefaults
import eu.torvian.chatbot.server.testutils.koin.defaultTestContainer
import kotlinx.coroutines.test.runTest
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

/**
 * Test class for [ApiKeyDaoExposed].
 */
class ApiKeyDaoExposedTest {

    private lateinit var container: DIContainer
    private lateinit var apiKeyDao: ApiKeyDao
    private lateinit var testDataManager: TestDataManager

    // Test data
    private val testSecret = ApiSecretEntity(
        alias = "test-alias-1",
        encryptedCredential = "encrypted_secret1",
        wrappedDek = "encrypted_dek1",
        keyVersion = 1,
        createdAt = TestDefaults.DEFAULT_INSTANT_MILLIS,
        updatedAt = TestDefaults.DEFAULT_INSTANT_MILLIS
    )

    private val testApiKey = ApiKey(
        id = "test-alias-1",
        name = "Test API Key",
        description = "A test API key for unit testing"
    )

    @BeforeEach
    fun setUp() = runTest {
        container = defaultTestContainer()

        apiKeyDao = container.get()
        testDataManager = container.get()

        // Create required tables and insert test secret first (foreign key dependency)
        testDataManager.createTables(setOf(Table.API_SECRETS, Table.API_KEYS))
        testDataManager.insertApiSecret(testSecret)
    }

    @AfterEach
    fun tearDown() = runTest {
        testDataManager.cleanup()
        container.close()
    }

    @Test
    fun `insertApiKey should create new API key successfully`() = runTest {
        // When
        val createdApiKey = apiKeyDao.insertApiKey(testApiKey.id, testApiKey.name, testApiKey.description)

        // Then
        assertEquals(testApiKey.id, createdApiKey.id)
        assertEquals(testApiKey.name, createdApiKey.name)
        assertEquals(testApiKey.description, createdApiKey.description)

        // Verify it was actually inserted
        val retrieved = testDataManager.getApiKey(testApiKey.id)
        assertNotNull(retrieved)
        assertEquals(testApiKey, retrieved)
    }

    @Test
    fun `getApiKeyById should return API key when it exists`() = runTest {
        // Given
        testDataManager.insertApiKey(testApiKey)

        // When
        val result = apiKeyDao.getApiKeyById(testApiKey.id)

        // Then
        assertTrue(result.isRight())
        val retrievedApiKey = result.getOrElse { throw AssertionError("Expected Right but got Left") }
        assertEquals(testApiKey, retrievedApiKey)
    }

    @Test
    fun `getApiKeyById should return ApiKeyNotFound when API key does not exist`() = runTest {
        // Given
        val nonExistentId = "non-existent-id"

        // When
        val result = apiKeyDao.getApiKeyById(nonExistentId)

        // Then
        assertTrue(result.isLeft())
        val error = result.fold({ it }, { throw AssertionError("Expected Left but got Right") })
        assertTrue(error is ApiKeyError.ApiKeyNotFound)
        assertEquals(nonExistentId, error.id)
    }

    @Test
    fun `getAllApiKeys should return all API keys`() = runTest {
        // Given
        val apiKey2 = testApiKey.copy(id = "test-alias-2", name = "Second API Key")
        val testSecret2 = testSecret.copy(alias = "test-alias-2")
        
        testDataManager.insertApiSecret(testSecret2)
        testDataManager.insertApiKey(testApiKey)
        testDataManager.insertApiKey(apiKey2)

        // When
        val result = apiKeyDao.getAllApiKeys()

        // Then
        assertEquals(2, result.size)
        assertTrue(result.contains(testApiKey))
        assertTrue(result.contains(apiKey2))
    }

    @Test
    fun `getAllApiKeys should return empty list when no API keys exist`() = runTest {
        // When
        val result = apiKeyDao.getAllApiKeys()

        // Then
        assertTrue(result.isEmpty())
    }

    @Test
    fun `updateApiKey should update existing API key successfully`() = runTest {
        // Given
        testDataManager.insertApiKey(testApiKey)
        val updatedApiKey = testApiKey.copy(name = "Updated Name", description = "Updated Description")

        // When
        val result = apiKeyDao.updateApiKey(updatedApiKey)

        // Then
        assertTrue(result.isRight())

        // Verify the update
        val retrieved = testDataManager.getApiKey(testApiKey.id)
        assertNotNull(retrieved)
        assertEquals(updatedApiKey, retrieved)
    }

    @Test
    fun `updateApiKey should return ApiKeyNotFound when API key does not exist`() = runTest {
        // Given
        val nonExistentApiKey = testApiKey.copy(id = "non-existent-id")

        // When
        val result = apiKeyDao.updateApiKey(nonExistentApiKey)

        // Then
        assertTrue(result.isLeft())
        val error = result.fold({ it }, { throw AssertionError("Expected Left but got Right") })
        assertTrue(error is ApiKeyError.ApiKeyNotFound)
        assertEquals(nonExistentApiKey.id, error.id)
    }

    @Test
    fun `deleteApiKey should delete existing API key successfully`() = runTest {
        // Given
        testDataManager.insertApiKey(testApiKey)

        // When
        val result = apiKeyDao.deleteApiKey(testApiKey.id)

        // Then
        assertTrue(result.isRight())

        // Verify deletion
        val retrieved = testDataManager.getApiKey(testApiKey.id)
        assertNull(retrieved)
    }

    @Test
    fun `deleteApiKey should return ApiKeyNotFound when API key does not exist`() = runTest {
        // Given
        val nonExistentId = "non-existent-id"

        // When
        val result = apiKeyDao.deleteApiKey(nonExistentId)

        // Then
        assertTrue(result.isLeft())
        val error = result.fold({ it }, { throw AssertionError("Expected Left but got Right") })
        assertTrue(error is ApiKeyError.ApiKeyNotFound)
        assertEquals(nonExistentId, error.id)
    }
}
