package eu.torvian.chatbot.common.models

import kotlinx.serialization.Serializable

/**
 * Represents an API key configuration in the chatbot system.
 * 
 * This model is used for managing API keys that are securely stored and referenced
 * by their unique identifier. The actual credential is stored separately using
 * the CredentialManager system.
 *
 * @property id The unique identifier for the API key (references ApiSecretEntity.alias)
 * @property name The display name for the API key (not unique, used for user identification)
 * @property description Optional description providing additional context about the API key
 */
@Serializable
data class ApiKey(
    val id: String,
    val name: String,
    val description: String
)
